import { Trash2, TrendingUp, AlertTriangle, ExternalLink, RefreshCw, Activity, TrendingDown } from 'lucide-react';
import { Stock } from '@/types';
import { useBatchStockData } from '@/hooks/useStockData';
import { detectVPattern } from '@/utils/patternDetection';
import { useMemo } from 'react';
import { MiniFlowChart } from '@/components/DataDisplay/MiniFlowChart';
import { formatMoneyAuto } from '@/utils/formatters';

interface StockListProps {
  stocks: Stock[];
  onRemoveStock: (code: string) => void;
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
  /** 是否显示实时监控数据 */
  showRealTimeData?: boolean;
  /** 是否全屏显示 */
  isFullScreen?: boolean;
}

export function StockList({
  stocks,
  onRemoveStock,
  onSelectStock,
  selectedStock,
  showRealTimeData = false,
  isFullScreen = false
}: StockListProps) {
  // 获取股票代码列表
  const stockCodes = stocks.map(s => s.code);

  // 批量获取股票数据（仅在显示实时数据时）
  const {
    results,
    isLoading: dataLoading,
    isFetching,
    refetch
  } = useBatchStockData(stockCodes, 20, {
    refetchInterval: showRealTimeData ? 60000 : undefined, // 60秒自动刷新
    enabled: showRealTimeData && stockCodes.length > 0
  });

  // 处理股票数据，集成V字型识别和实时数据
  const stocksWithData = useMemo(() => {
    if (!showRealTimeData || !results || Object.keys(results).length === 0) {
      return stocks.map(stock => ({ ...stock, data: null, hasVPattern: false, latestFlow: 0, change24h: 0 }));
    }

    return stocks.map(stock => {
      const data = results[stock.code];
      const hasVPattern = data?.klines ? detectVPattern(data.klines).hasVPattern : false;

      // 计算最新流入和变化
      const latestFlow = data?.klines?.[data.klines.length - 1]?.mainNetInflow || 0;
      const change24h = data?.klines && data.klines.length >= 2
        ? data.klines[data.klines.length - 1].mainNetInflow - data.klines[data.klines.length - 2].mainNetInflow
        : 0;

      return {
        ...stock,
        data,
        hasVPattern,
        latestFlow,
        change24h,
      };
    });
  }, [stocks, results, showRealTimeData]);
  if (stocks.length === 0) {
    return (
      <div className={`text-center ${isFullScreen ? 'py-16' : 'py-8'}`}>
        <TrendingUp className={`${isFullScreen ? 'w-20 h-20' : 'w-12 h-12'} text-gray-300 mx-auto mb-3`} />
        <p className={`text-gray-500 mb-2 ${isFullScreen ? 'text-lg' : ''}`}>暂无股票代码</p>
        <p className={`${isFullScreen ? 'text-base' : 'text-sm'} text-gray-400`}>请添加股票代码开始监控</p>
      </div>
    );
  }

  return (
    <div className="space-y-2 h-full flex flex-col">
      {/* 列表头部 */}
      <div className={`flex items-center justify-between ${isFullScreen ? 'text-base' : 'text-sm'} text-gray-600 pb-2 border-b border-gray-200`}>
        <div className="flex items-center gap-2">
          <span>股票代码 ({stocks.length})</span>
          {showRealTimeData && (
            <>
              {isFetching && (
                <RefreshCw className={`${isFullScreen ? 'w-4 h-4' : 'w-3 h-3'} animate-spin text-blue-500`} />
              )}
              <button
                onClick={() => refetch()}
                className={`${isFullScreen ? 'text-sm' : 'text-xs'} text-gray-500 hover:text-gray-700 transition-colors`}
                disabled={isFetching}
                title="刷新实时数据"
              >
                <Activity className={`${isFullScreen ? 'w-4 h-4' : 'w-3 h-3'}`} />
              </button>
            </>
          )}
        </div>
        <span>操作</span>
      </div>

      {/* 股票列表 - 三列网格布局 */}
      <div className={`flex-1 ${isFullScreen ? 'overflow-hidden' : 'max-h-64 overflow-y-auto'}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
          {stocksWithData.map((stock) => (
            <StockListItem
              key={stock.code}
              stock={stock}
              isSelected={selectedStock === stock.code}
              onSelect={onSelectStock}
              onRemove={onRemoveStock}
              showRealTimeData={showRealTimeData}
              stockData={stock.data}
              hasVPattern={stock.hasVPattern}
              latestFlow={stock.latestFlow}
              change24h={stock.change24h}
              isFullScreen={isFullScreen}
            />
          ))}
        </div>
      </div>
      
      {/* 批量操作和状态信息 */}
      {stocks.length > 1 && (
        <div className="pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <button
              onClick={() => {
                if (window.confirm('确定要清空所有股票代码吗？')) {
                  stocks.forEach(stock => onRemoveStock(stock.code));
                }
              }}
              className="text-sm text-danger-600 hover:text-danger-700 flex items-center gap-1"
            >
              <AlertTriangle className="w-4 h-4" />
              清空所有
            </button>

            {showRealTimeData && (
              <div className="text-xs text-gray-500">
                {dataLoading ? '加载中...' : `实时监控已启用`}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

interface StockListItemProps {
  stock: Stock;
  isSelected?: boolean;
  onSelect?: (code: string) => void;
  onRemove: (code: string) => void;
  showRealTimeData?: boolean;
  stockData?: any;
  hasVPattern?: boolean;
  latestFlow?: number;
  change24h?: number;
  isFullScreen?: boolean;
}

function StockListItem({
  stock,
  isSelected,
  onSelect,
  onRemove,
  showRealTimeData = false,
  stockData,
  hasVPattern = false,
  latestFlow = 0,
  change24h = 0,
  isFullScreen = false
}: StockListItemProps) {
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm(`确定要删除股票 ${stock.code} 吗？`)) {
      onRemove(stock.code);
    }
  };

  const handleSelect = () => {
    if (onSelect) {
      onSelect(stock.code);
    }
  };

  const handleViewFlow = (e: React.MouseEvent) => {
    e.stopPropagation();
    const url = `https://data.eastmoney.com/zjlx/${stock.code}.html`;
    window.open(url, '_blank');
  };

  // 获取中国股市颜色（红涨绿跌）
  const getChineseStockColor = (value: number) => {
    if (value > 0) {
      return 'text-red-600'; // 红色 - 上涨/流入
    } else if (value < 0) {
      return 'text-green-600'; // 绿色 - 下跌/流出
    } else {
      return 'text-gray-600'; // 灰色 - 无变化
    }
  };

  return (
    <div
      className={`
        flex flex-col ${isFullScreen ? 'p-4' : 'p-3'} rounded-lg border transition-all duration-200
        ${isSelected
          ? 'border-primary-500 bg-primary-50'
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }
        ${onSelect ? 'cursor-pointer' : ''}
        min-h-[120px]
      `}
      onClick={handleSelect}
    >
      {/* 头部：股票信息和操作按钮 */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2 flex-1">
          {/* 股票代码 */}
          <div className="flex flex-col">
            <span className={`${isFullScreen ? 'text-base' : 'text-sm'} font-medium ${
              isSelected ? 'text-primary-700' : 'text-gray-900'
            }`}>
              {stock.name}
            </span>
            <span className={`${isFullScreen ? 'text-sm' : 'text-xs'} text-gray-500 font-mono`}>
              {stock.code}
            </span>
          </div>

          {/* 选中指示器 */}
          {isSelected && (
            <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
          )}
        </div>

        {/* 操作按钮组 */}
        <div className="flex items-center gap-1">
          {/* 查看资金流向按钮 */}
          <button
            onClick={handleViewFlow}
            className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
            title="查看资金流向"
          >
            <ExternalLink className="w-3 h-3" />
          </button>

          {/* 删除按钮 */}
          <button
            onClick={handleRemove}
            className="p-1 text-gray-400 hover:text-danger-600 transition-colors duration-200"
            title="删除股票"
          >
            <Trash2 className="w-3 h-3" />
          </button>
        </div>
      </div>

      {/* 实时监控数据 */}
      {showRealTimeData && (
        <div className="flex-1 flex flex-col gap-2">
          {stockData && stockData.klines ? (
            <>
              {/* 资金流向信息 */}
              <div className="flex flex-col gap-1">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">主力净流入</span>
                  <span
                    className={`font-bold text-sm ${getChineseStockColor(latestFlow)}`}
                  >
                    {formatMoneyAuto(latestFlow)}
                  </span>
                </div>

                {/* 24小时变化 */}
                {change24h !== 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">24h变化</span>
                    <div className="flex items-center gap-1">
                      {change24h > 0 ? (
                        <TrendingUp className="w-3 h-3 text-red-500" />
                      ) : (
                        <TrendingDown className="w-3 h-3 text-green-500" />
                      )}
                      <span
                        className={`text-xs font-medium ${getChineseStockColor(change24h)}`}
                      >
                        {change24h > 0 ? '+' : ''}{formatMoneyAuto(change24h)}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* 底部：V字形模式指示器和迷你图表 */}
              <div className="flex items-center justify-between mt-auto">
                {/* V字形模式指示器 */}
                {hasVPattern && (
                  <div className="flex items-center gap-1">
                    <Activity className="w-3 h-3 text-red-500" />
                    <span className="text-xs text-red-600 font-medium">V型</span>
                  </div>
                )}

                {/* 迷你图表 */}
                <div className="w-20 h-8 flex-shrink-0">
                  <MiniFlowChart
                    klines={stockData.klines}
                    height={32}
                    showVPattern={true}
                  />
                </div>
              </div>
            </>
          ) : (
            /* 无数据状态 */
            <div className="flex-1 flex items-center justify-center text-gray-400">
              <div className="text-center">
                <span className="text-xs">加载中...</span>
                <div className="w-full h-6 bg-gray-100 rounded mt-1 flex items-center justify-center">
                  <span className="text-xs text-gray-400">无数据</span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
